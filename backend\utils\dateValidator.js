/**
 * Date validation utilities for the TranceAsia_IMS system
 * Provides consistent date validation across the application
 */

/**
 * Validates that a date is not in the past
 * @param {string|Date} dateInput - The date to validate
 * @param {string} fieldName - Name of the field being validated (for error messages)
 * @returns {Object} - { isValid: boolean, error: string|null }
 */
const validateDateNotInPast = (dateInput, fieldName = 'Date') => {
  try {
    if (!dateInput) {
      return {
        isValid: false,
        error: `${fieldName} is required`
      };
    }

    const inputDate = new Date(dateInput);
    const today = new Date();
    
    // Check if the date is valid
    if (isNaN(inputDate.getTime())) {
      return {
        isValid: false,
        error: `${fieldName} must be a valid date`
      };
    }

    // Set both dates to start of day for accurate comparison
    today.setHours(0, 0, 0, 0);
    inputDate.setHours(0, 0, 0, 0);

    if (inputDate < today) {
      return {
        isValid: false,
        error: `${fieldName} cannot be in the past. Please select today's date or a future date.`
      };
    }

    return {
      isValid: true,
      error: null
    };
  } catch (error) {
    return {
      isValid: false,
      error: `${fieldName} validation failed: ${error.message}`
    };
  }
};

/**
 * Validates that a date is within a reasonable range (not too far in the future)
 * @param {string|Date} dateInput - The date to validate
 * @param {number} maxDaysInFuture - Maximum days in the future allowed (default: 365)
 * @param {string} fieldName - Name of the field being validated
 * @returns {Object} - { isValid: boolean, error: string|null }
 */
const validateDateRange = (dateInput, maxDaysInFuture = 365, fieldName = 'Date') => {
  try {
    if (!dateInput) {
      return {
        isValid: false,
        error: `${fieldName} is required`
      };
    }

    const inputDate = new Date(dateInput);
    const today = new Date();
    const maxFutureDate = new Date();
    maxFutureDate.setDate(today.getDate() + maxDaysInFuture);
    
    // Check if the date is valid
    if (isNaN(inputDate.getTime())) {
      return {
        isValid: false,
        error: `${fieldName} must be a valid date`
      };
    }

    // Set all dates to start of day for accurate comparison
    today.setHours(0, 0, 0, 0);
    inputDate.setHours(0, 0, 0, 0);
    maxFutureDate.setHours(0, 0, 0, 0);

    if (inputDate < today) {
      return {
        isValid: false,
        error: `${fieldName} cannot be in the past. Please select today's date or a future date.`
      };
    }

    if (inputDate > maxFutureDate) {
      return {
        isValid: false,
        error: `${fieldName} cannot be more than ${maxDaysInFuture} days in the future.`
      };
    }

    return {
      isValid: true,
      error: null
    };
  } catch (error) {
    return {
      isValid: false,
      error: `${fieldName} validation failed: ${error.message}`
    };
  }
};

/**
 * Formats a date to YYYY-MM-DD format for database storage
 * @param {string|Date} dateInput - The date to format
 * @returns {string} - Formatted date string
 */
const formatDateForDB = (dateInput) => {
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date provided for formatting');
  }
  return date.toISOString().split('T')[0];
};

/**
 * Gets today's date in YYYY-MM-DD format
 * @returns {string} - Today's date formatted for HTML date inputs
 */
const getTodayFormatted = () => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Comprehensive purchase date validation
 * Validates that the purchase date is not in the past and not too far in the future
 * @param {string|Date} dateInput - The purchase date to validate
 * @returns {Object} - { isValid: boolean, error: string|null }
 */
const validatePurchaseDate = (dateInput) => {
  // First check if date is not in the past
  const pastValidation = validateDateNotInPast(dateInput, 'Purchase date');
  if (!pastValidation.isValid) {
    return pastValidation;
  }

  // Then check if date is within reasonable range (1 year in future)
  const rangeValidation = validateDateRange(dateInput, 365, 'Purchase date');
  return rangeValidation;
};

module.exports = {
  validateDateNotInPast,
  validateDateRange,
  validatePurchaseDate,
  formatDateForDB,
  getTodayFormatted
};
