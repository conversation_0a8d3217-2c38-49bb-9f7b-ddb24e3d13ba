{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.15", "@types/react-datepicker": "^7.0.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "dot": "^1.1.3", "env": "^0.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "pdf": "^0.1.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.4.0", "react-select": "^5.10.1", "react-to-pdf": "^2.0.0", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "tailwindcss": "^4.0.15", "to": "^0.2.9"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}