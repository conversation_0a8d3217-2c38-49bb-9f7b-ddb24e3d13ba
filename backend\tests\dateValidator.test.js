/**
 * Test file for date validation utilities
 * Run with: node backend/tests/dateValidator.test.js
 */

const { 
  validateDateNotInPast, 
  validateDateRange, 
  validatePurchaseDate,
  formatDateForDB,
  getTodayFormatted 
} = require('../utils/dateValidator');

// Simple test runner
function runTest(testName, testFunction) {
  try {
    testFunction();
    console.log(`✅ ${testName} - PASSED`);
  } catch (error) {
    console.log(`❌ ${testName} - FAILED: ${error.message}`);
  }
}

function assert(condition, message) {
  if (!condition) {
    throw new Error(message);
  }
}

// Test validateDateNotInPast
runTest('validateDateNotInPast - Today should be valid', () => {
  const today = new Date().toISOString().split('T')[0];
  const result = validateDateNotInPast(today);
  assert(result.isValid === true, 'Today should be valid');
  assert(result.error === null, 'No error should be returned');
});

runTest('validateDateNotInPast - Future date should be valid', () => {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowStr = tomorrow.toISOString().split('T')[0];
  
  const result = validateDateNotInPast(tomorrowStr);
  assert(result.isValid === true, 'Future date should be valid');
  assert(result.error === null, 'No error should be returned');
});

runTest('validateDateNotInPast - Past date should be invalid', () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayStr = yesterday.toISOString().split('T')[0];
  
  const result = validateDateNotInPast(yesterdayStr);
  assert(result.isValid === false, 'Past date should be invalid');
  assert(result.error.includes('cannot be in the past'), 'Error should mention past date');
});

runTest('validateDateNotInPast - Empty date should be invalid', () => {
  const result = validateDateNotInPast('');
  assert(result.isValid === false, 'Empty date should be invalid');
  assert(result.error.includes('required'), 'Error should mention required');
});

runTest('validateDateNotInPast - Invalid date should be invalid', () => {
  const result = validateDateNotInPast('invalid-date');
  assert(result.isValid === false, 'Invalid date should be invalid');
  assert(result.error.includes('valid date'), 'Error should mention valid date');
});

// Test validateDateRange
runTest('validateDateRange - Date within range should be valid', () => {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + 30);
  const futureDateStr = futureDate.toISOString().split('T')[0];
  
  const result = validateDateRange(futureDateStr, 365);
  assert(result.isValid === true, 'Date within range should be valid');
  assert(result.error === null, 'No error should be returned');
});

runTest('validateDateRange - Date too far in future should be invalid', () => {
  const farFutureDate = new Date();
  farFutureDate.setDate(farFutureDate.getDate() + 400);
  const farFutureDateStr = farFutureDate.toISOString().split('T')[0];
  
  const result = validateDateRange(farFutureDateStr, 365);
  assert(result.isValid === false, 'Date too far in future should be invalid');
  assert(result.error.includes('more than'), 'Error should mention range limit');
});

// Test validatePurchaseDate
runTest('validatePurchaseDate - Valid purchase date should pass', () => {
  const validDate = new Date();
  validDate.setDate(validDate.getDate() + 7);
  const validDateStr = validDate.toISOString().split('T')[0];
  
  const result = validatePurchaseDate(validDateStr);
  assert(result.isValid === true, 'Valid purchase date should pass');
  assert(result.error === null, 'No error should be returned');
});

runTest('validatePurchaseDate - Past purchase date should fail', () => {
  const pastDate = new Date();
  pastDate.setDate(pastDate.getDate() - 1);
  const pastDateStr = pastDate.toISOString().split('T')[0];
  
  const result = validatePurchaseDate(pastDateStr);
  assert(result.isValid === false, 'Past purchase date should fail');
  assert(result.error.includes('cannot be in the past'), 'Error should mention past date');
});

// Test formatDateForDB
runTest('formatDateForDB - Should format date correctly', () => {
  const date = new Date('2024-01-15T10:30:00Z');
  const result = formatDateForDB(date);
  assert(result === '2024-01-15', 'Date should be formatted as YYYY-MM-DD');
});

runTest('formatDateForDB - Should handle string input', () => {
  const result = formatDateForDB('2024-01-15');
  assert(result === '2024-01-15', 'String date should be formatted correctly');
});

// Test getTodayFormatted
runTest('getTodayFormatted - Should return today in correct format', () => {
  const result = getTodayFormatted();
  const today = new Date().toISOString().split('T')[0];
  assert(result === today, 'Should return today in YYYY-MM-DD format');
});

console.log('\n🧪 Date Validation Tests Complete');
console.log('Run this test file to verify date validation is working correctly.');
console.log('Usage: node backend/tests/dateValidator.test.js');
