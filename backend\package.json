{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "init-repair-db": "node scripts/initRepairDb.js", "create-purchase-undo-logs-table": "node scripts/createPurchaseUndoLogsTable.js", "fix-purchase-undo-logs-table": "node scripts/fixPurchaseUndoLogsTable.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "mysql": "^2.18.1", "mysql2": "^3.14.0", "node": "^20.19.0", "nodemailer": "^7.0.3", "socket.io": "^4.8.1"}}